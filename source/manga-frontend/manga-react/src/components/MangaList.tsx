import { useEffect, useState } from 'react';
import MangaCard from './MangaCard';
import mangaService from '../services/manga-service';
import { MangaResponse } from '../interfaces/models/manga';

const MangaList: React.FC = () => {
    const [mangaList, setMangaList] = useState<MangaResponse[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchMangaSummaries = async () => {
            try {
                setLoading(true);
                const result = await mangaService.getMangaSummaries(0, 18, "lastChapterAddedAt,desc");

                if (result) {
                    // Sử dụng trực tiếp MangaResponse từ API
                    setMangaList(result.content);
                    setError(null);
                } else {
                    setError("Không thể tải danh sách manga");
                }
            } catch (err) {
                console.error("Lỗi khi tải danh sách manga:", err);
                setError("Đã xảy ra lỗi khi tải danh sách manga");
            } finally {
                setLoading(false);
            }
        };

        fetchMangaSummaries();
    }, []);

    return (
        <div className="flex-grow min-h-screen">
            <div className="mx-2 py-8 lg:py-16">
                <div className="common-container mb-8 lg:mb-12">
                    <div className="uppercase font-bold text-xl text-gray-300">
                        Mới cập nhật
                    </div>
                </div>

                <div className="common-container">
                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                    ) : error ? (
                        <div className="text-red-500 text-center py-8">{error}</div>
                    ) : (
                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            {mangaList.map((manga) => (
                                <MangaCard key={manga.id} manga={manga} />
                            ))}
                        </div>
                    )}
                </div>
                <div className="flex justify-end mt-5">
                    <a
                        href="/newest"
                        className="flex items-center text-gray-600 hover:text-blue-600 transition text-sm font-bold cursor-pointer"
                    >
                        {/* Chevron Right Icon */}
                        <span className="mr-1">{'>'} </span>
                        {/* Text */}
                        <span>Xem danh sách truyện</span>
                    </a>
                </div>
            </div>

        </div>
    );
};

export default MangaList;